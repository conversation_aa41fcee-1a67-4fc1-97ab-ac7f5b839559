# Agar.io Clone with Solana Integration — Project Plan and Execution Checklist

This document defines the technical plan, phases, scope, and design system for building a continuous Agar.io-style lobby with Solana wagering, dynamic oracle-based SOL conversion, and a server-verified cash-out mechanism.

Stack Confirmed (Defaults)

- Client: Custom Canvas/WebGL rendering with HTML5 Canvas 2D (option to upgrade parts to WebGL later), real-time networking via Socket.IO.
- Server: Node.js authoritative game server (TypeScript), Socket.IO, deterministic simulation loop, PostgreSQL for persistence, Redis for pub/sub and match state snapshots.
- Solana: Anchor program (escrow, wagers, payouts), @solana/web3.js on client/server, PDAs for user and lobby state.
- Oracle: Pyth SOL/USD with confidence intervals and circuit-breakers.
- Cluster: Develop on Devnet, target Mainnet-beta. Wallets: Phantom, Solflare, Backpack.
- Anti-cheat: Authoritative server simulation with snapshotting and input reconciliation; server-only movement/collision; signed inputs with rate-limits.
- Compliance: Geo/IP gating baseline with regional policy configuration.

---

Project Structure

- root/
  - packages/
    - client/ (React-lite SPA or vanilla TS app)
      - src/
        - core/ (rendering engine, camera, input)
        - net/ (socket client, reconciliation)
        - game/ (UI states, HUD, menu)
        - solana/ (wallet adapters, Anchor IDL, tx utils)
        - assets/ (images, fonts, sounds)
        - styles/ (global.css, tokens.css)
        - index.html
        - main.ts
      - vite.config.ts
      - package.json
    - server/
      - src/
        - app.ts (http server + socket.io)
        - auth/ (sessions, JWT, wallet link)
        - game/
          - loop.ts (main simulation loop)
          - state.ts (entities, quadtrees, collisions)
          - systems/ (decay, viruses, pellets, split/merge, eject)
          - reconcile.ts (input reconciliation)
        - net/ (socket handlers, schema validation)
        - solana/ (anchor client, tx building, pyth price fetch)
        - db/ (prisma or typeorm models)
        - compliance/ (geo/IP gating, feature flags)
        - config/
          - env.ts
          - lobby.ts (wager tiers, oracle margins, fees)
      - package.json
    - anchor-program/
      - programs/agar_escrow/
        - src/lib.rs
      - Anchor.toml
      - Cargo.toml
      - migrations/
      - tests/
  - infra/
    - docker/
      - server.Dockerfile
      - nginx.Dockerfile
    - k8s/ (optional later)
    - terraform/ (optional later)
    - scripts/ (deployment, migration)
  - docs/
    - architecture.md
    - whitepaper.md
    - idl/ (generated Anchor IDL)
  - .env.example
  - package.json
  - pnpm-workspace.yaml or turborepo.json

---

Design System and Styling Guide

Typography

- Font family: Inter for UI and numbers; Fallbacks: system-ui, -apple-system, Segoe UI, Roboto.
- Scale:
  - Display: 48/56, weight 700, tracking -0.02em
  - H1: 36/44, weight 700
  - H2: 28/36, weight 600
  - H3: 22/30, weight 600
  - Body: 16/24, weight 400
  - Caption/Mono (for SOL, addresses): 13/18, weight 500, font-variant-numeric: tabular-nums.

Color

- Background: #0B0F14 (primary), #0E141B (panel)
- Primary: #38BDF8 (info/callouts)
- Accent: #A78BFA (interactive highlights)
- Success: #22C55E
- Warning: #F59E0B
- Error: #EF4444
- Neutral: #94A3B8 (text secondary), #CBD5E1 (borders)
- Game field: deep muted blue-green gradient; pellets: light neutrals; viruses: spiky green with bloom; players: distinct hues with luminance contrast.

UI Components (atoms/molecules/organisms)

- Atoms: Button, IconButton, Toggle, Tooltip, Badge, ProgressBar, Tag, Avatar, Alert, Modal, TextInput, Select, Checkbox, Radio, Slider.
- Molecules: WalletConnect, SolAmountField (with fiat conversion, oracle rate), LobbyCard (wager tier, live players), UsernameForm, CashoutHoldButton (press-and-hold UI with radial timer), BalanceWidget (SOL, USD est), GeoGateBanner, Toasts.
- Organisms: OnboardingDialog (account + wallet link), DepositFlow (amount, wallet approve), LobbyBrowser (continuous lobbies), HUD (score, mass, latency, cash-out, balance), SettingsPanel (controls, graphics, audio), ComplianceNotice.
- Interaction motion: 120–180ms ease-out for UI; 60fps for game loop rendering; haptics via simple sound cues.

Accessibility

- WCAG AA contrast for UI text on dark backgrounds.
- Keyboard navigation across all menus. Focus ring: 2px #38BDF8 at 50% opacity.
- Colorblind-friendly player hue assignments; avoid red/green-only contrast.

---

Game Design Specifications

Continuous Lobby

- Always-on world. New players spawn at periphery away from high-density areas using spatial hashing to find low-threat regions.
- Wager tiers: $1 and $10 USD-equivalent in SOL, dynamic using Pyth price with configurable safety margin (e.g., +0.5%).
- Player username picked per entry; server enforces profanity blacklist and uniqueness per session.

Core Mechanics

- Mass decay: server-driven exponential or linear decay (configurable) scaled by total mass (encourages movement).
- Pellets: static spawns, server seeds deterministic zones; pellets carry no monetary value but affect in-game mass.
- Eject (W): deducts small mass from player; generates new pellets; tuning TBD via config and runtime flags.
- Split (Space): split cell into two (or more up to a cap, e.g., 16) without loss; server enforces merge cooldown (e.g., 20s).
- Viruses:
  - Large cells hit: burst split into many pieces (up to max cap) and gain additional mass making them vulnerable.
  - Small cells: pass harmlessly; visibility exception near larger cells as design affordance.
  - Feeding viruses with W creates new virus ejection in movement direction when threshold is reached; visual “spiky” pre-pop cue.
- Collisions and capture: deterministic physics based on cell radii proportional to sqrt(mass/pi) with speed inversely proportional to mass.
- Maximum cells cap enforced server-side; if already at cap, virus consumption gives mass without further split.

Value Mapping

- Entry wager SOL → initial mass via formula: mass = base_mass + (wager_SOL \* oracle_SOL_to_mass_factor).
- On elimination: 100% of victim’s current mass value transfers to killer’s value pool; in-game mass merges immediately.
- Cash out conversion: current mass value converted back to SOL using latest oracle price minus platform fee (configurable, e.g., 5%).

---

Solana Program Design (Anchor)

Accounts (PDAs)

- GlobalConfig: fees, oracle addresses, authority, paused flags, geo-gate toggles.
- Lobby: unique for each wager tier; holds escrow SOL pool and config (entry min SOL, oracle safety margin).
- UserState: per user (by wallet), tracks deposited balance (unused), last cash-out timestamps, KYC/geo flags.
- Session: per active game entry; links user, lobby, initial wager SOL, and server-issued session token/hash for anti-tamper.
- Treasury (Escrow): PDA-owned system account accumulating wagers and holding reserved cash-out amounts.

Instructions

- initialize_global (admin)
- create_lobby (admin)
- deposit (user → treasury PDA) [User pays transaction fee]
- enter_lobby (wager approval; mints/allocates session record)
- record_elimination (server-side signer with admin authority; optional post-game audit)
- cash_out (convert mass value → SOL, apply platform fee, transfer to user wallet)
- withdraw_unused (user withdraws unspent deposited SOL)
- set_pause / set_geo / update_fees (admin)

Security Model

- Admin authority key managed by server in HSM or KMS; server never holds user private keys.
- Price oracle read from Pyth price account with confidence checks; if stale or wide confidence, block tx with user-friendly UI.
- Circuit breaker: cap on per-transaction payout; rolling limits; optional timeouts during oracle volatility.
- Replay protection via session token nonces; sessions expire when user cashes out or is eliminated.

---

Networking and Anti-Cheat

Authoritative Server

- Server simulates full game state at tick rate 30–60 TPS; client sends inputs only (movement vector, actions).
- Snapshot and delta compression; client-side interpolation and rewind/replay for input reconciliation.
- Rate limits: per-socket message frequency; signature-based input tokens bound to Session account.
- Cheat prevention:
  - Server validates velocity, acceleration, split/eject cooldowns.
  - Server-only collision resolution.
  - Server timestamp and drift detection; drop out-of-order packets beyond window.

State Sync

- Socket channels: lobby:join, lobby:state, player:input, player:split, player:eject, player:cashout:init, player:cashout:confirm, server:notice, error.
- Binary packing for hot paths using ArrayBuffer/DataView; retain JSON for control path for faster iteration.

---

Compliance and Risk

- Geo/IP gating with maintained list of restricted regions; Cloudflare Workers or server middleware for country code checks.
- Responsible gaming: limits for daily deposits and cashouts; self-exclusion flags in UserState.
- Legal review for skill vs chance; disclaimers and ToS acceptance gating.
- Data retention policy for transaction and session logs.

---

Configuration and Tuning

- env flags:
  - SOLANA_CLUSTER=devnet/mainnet-beta
  - PYTH_SOL_PRICE_ACCOUNT=...
  - PLATFORM_FEE_BPS=500
  - ORACLE_SAFETY_MARGIN_BPS=50
  - CASHOUT_HOLD_MS=5000
  - MERGE_COOLDOWN_MS=20000
  - MAX_CELLS=16
  - DECAY_RATE=...
- Lobby tiers defined in server config: $1, $10; SOL computed server-side from Pyth with margin and min_bet rounding.

---

Testing and QA

- Unit: physics systems (collision, split, eject), oracle conversions, fee math.
- Integration: end-to-end wallet connect → deposit → enter lobby → play → cash out on devnet.
- Property tests on server physics determinism (seeded RNG).
- Soak tests: 200–500 simulated bots; measure tick latency and packet loss.
- Security tests: replay, packet tamper, rate-limit bypass, oracle stale data, payout caps.

---

Mermaid Diagrams

User Onboarding and Flow

```mermaid
flowchart TD
  A[Landing] --> B[Create Account]
  B --> C[Connect Wallet (Phantom/Solflare/Backpack)]
  C --> D[Deposit SOL to Treasury PDA]
  D --> E[Select Continuous Lobby (Wager Tier)]
  E --> F[Approve Wager Tx (enter_lobby)]
  F --> G[Spawn in World]
  G --> H{Play}
  H -->|Eliminated| I[Value transfers to Killer; Session ends]
  H -->|Hold Q 5s| J[Server verifies; cash_out instruction]
  J --> K[SOL sent to wallet minus platform fee]
  I --> L[Browser return to Lobby]
  K --> L
```

Server Authoritative Loop

```mermaid
sequenceDiagram
  participant Client
  participant Server
  loop Every tick
    Client->>Server: Input (dir, split/eject flags) + session token
    Server-->>Client: Snapshot/Delta (positions, masses, pellets, viruses)
  end
  Client->>Server: Cashout init (hold Q)
  Server->>Server: Verify hold time, freeze inputs for player
  Server->>Solana: cash_out(Session, UserState, Lobby, Treasury)
  Solana-->>Server: Confirmation
  Server-->>Client: Removal + Toast + Tx Signature
```

Anchor Accounts

```mermaid
erDiagram
  GLOBALCONFIG ||--o{ LOBBY : owns
  USERSTATE ||--o{ SESSION : has
  LOBBY ||--o{ SESSION : contains
  TREASURY ||--o{ LOBBY : funds
```

---

Phase Plan and Checklist

Phase 1: Setup & Configuration

- [ ] Initialize monorepo (pnpm or turbo), create packages: client, server, anchor-program.
- [ ] Configure TypeScript, ESLint, Prettier, commit hooks.
- [ ] Add .env management with schema validation on both client and server.
- [ ] Set up Docker for server, Postgres, Redis; docker-compose for local dev.
- [ ] Create basic README with run instructions.

Phase 2: Solana Program (Anchor)

- [ ] Define PDAs: GlobalConfig, Lobby, UserState, Session, Treasury.
- [ ] Implement instructions: initialize_global, create_lobby, deposit, enter_lobby, cash_out, withdraw_unused, admin updates.
- [ ] Integrate Pyth price read and validation in relevant instructions.
- [ ] Unit tests (Anchor mocha) for deposits, wagers, cashouts, fees.
- [ ] Generate IDL and publish to docs/idl.

Phase 3: Backend Server (Authoritative)

- [ ] HTTP + Socket.IO bootstrap with health checks.
- [ ] DB schema (Prisma/TypeORM): users, sessions, tx logs, geo flags.
- [ ] Game loop: deterministic tick, RNG seeding, spatial partitioning (quadtrees).
- [ ] Systems: movement, collision, consume, split, merge, eject, decay, pellets, virus logic.
- [ ] Input validation, rate limiting, signature/nonce checks, reconciliation.
- [ ] Oracle client (Pyth) with staleness/confidence gating and circuit breaker.
- [ ] Solana client: tx builders for deposit, enter_lobby, cash_out, withdraw_unused.
- [ ] Logging/metrics (pino, prometheus), error categorization.

Phase 4: Client Application

- [ ] Bootstrap Vite + TS; state management (Zustand or minimal custom).
- [ ] Canvas renderer: camera, layers, culling, interpolation.
- [ ] Input handling: mouse, keyboard (W/Space/Q), mobile fallback later.
- [ ] UI: Onboarding, WalletConnect, DepositFlow, LobbyBrowser, UsernameForm.
- [ ] HUD: mass, score, ping, cash-out hold button with server-timed progress.
- [ ] Visuals: pellets, viruses, player cells, split/eject effects; scaling with DPI.
- [ ] Network: Socket client, reconciliation, binary packing for hot updates.
- [ ] Accessibility and responsive layout.

Phase 5: Cash-Out & Economy Integration

- [ ] Map wager SOL to initial mass; tune oracle margin and conversion factor.
- [ ] Implement server-side 5s Q hold verification and freeze behavior.
- [ ] Execute cash_out instruction; await confirmation; update state; remove player.
- [ ] Platform fee application and accounting; receipts and UI toasts.

Phase 6: Compliance & Gating

- [ ] Geo/IP gating middleware and UI banner.
- [ ] ToS acceptance, disclaimers, responsible gaming limits in UserState.
- [ ] Admin toggles: pause, fee updates, geo rules, lobby parameters.

Phase 7: QA, Tuning, and Soak Testing

- [ ] Bot framework for load; record tick and net metrics.
- [ ] Balance tuning: decay rate, pellet density, virus thresholds, speeds.
- [ ] Security tests (replay, tamper, oracle stale).
- [ ] Usability testing and polish.

Phase 8: Deployment

- [ ] CI/CD pipelines (GitHub Actions): lint, test, build, Anchor deploy (devnet).
- [ ] Provision Postgres, Redis, server containers; NGINX reverse proxy with SSL.
- [ ] Observability: Grafana/Prometheus dashboards; log shipping.
- [ ] Feature flags for incremental rollout; error budgets.

---

Detailed Task Breakdown by Component

Client

- Rendering: entity sprites, vector outlines, shader-like effects via canvas compositing.
- Camera: scale by mass, smooth zoom, offscreen culling.
- HUD: dynamic updates, latency meter; wallet status and balances.
- Wallet integration: Phantom/Solflare/Backpack; handle disconnections; show tx signatures.
- Cash-out UX: press-and-hold with server-side timer; disabled if staleness/volatility conditions fail.

Server

- Physics: radii r = k \* sqrt(mass); speed v = base / (1 + mass^alpha).
- Quadtrees: query for neighbors, pellet fields; resolve collisions in deterministic order.
- Events: elimination → value transfer; broadcast deltas.
- Persistence: session logs, tx refs, audit trail.
- Oracle: background task to refresh price; provide signed price info to client (display only).

Anchor Program

- Fees: platform_fee_bps stored in GlobalConfig; adjustable by admin.
- Cash-out: compute SOL using Pyth price and deduct fee; transfer from Treasury PDA to user.
- Withdraw unused: from UserState tracked balance.
- Session: holds wager, lobby ref, and server-issued session hash.

---

Open Questions to Tackle During Implementation (tracked in issues)

- Exact numeric tuning for decay, eject, virus thresholds.
- Visual treatment for near-virus visibility exception.
- Binary vs JSON proportions in network payloads; when to switch fully to binary.
- Custody of admin key (KMS options) and governance for parameter changes.

---

Risks and Mitigations

- Oracle volatility: safety margins, stale-price rejection, circuit breakers.
- DDoS/Spam: per-IP and per-socket rate limits, Cloudflare, bot detection, PoW challenge if needed.
- Cheating: authoritative server, reconciliation caps, snapshot audits.
- Regulatory: geo gating, logs, ToS, limits.

---

Deliverables

- Running devnet MVP: deposit → enter → play → eliminate/cash-out → withdraw unused.
- Documentation: architecture, API contracts, IDL, runbooks.
- Design assets: UI library, icon set, and sound cues list.
